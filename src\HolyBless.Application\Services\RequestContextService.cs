using System;
using Amazon.S3.Model.Internal.MarshallTransformations;
using HolyBless.Enums;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.DependencyInjection;
using static Volo.Abp.Http.MimeTypes;

namespace HolyBless.Services
{
    /// <summary>
    /// Service for accessing request context information like language codes from headers
    /// </summary>
    [RemoteService(false)]
    public class RequestContextService : ApplicationService, IRequestContextService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        // Header names that <PERSON><PERSON> will send, "Accept-Language" is ABP's default header for language codes
        private const string LanguageCodeHeader = "Accept-Language"; //"x-user-language-reading";

        private const string SpokenLangCodeHeader = "X-User-Language-Audio";
        private const string UserCountryHeader = "X-User-Language-Country";
        private const string UserGuidHeader = "X-User-UUID";

        public RequestContextService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        /// <summary>
        /// Gets the LanguageCode from the request header
        /// </summary>
        /// <returns>The language code or null if not present</returns>
        public string? GetLanguageCode()
        {
            var lang = GetRequestHeader(LanguageCodeHeader) ?? LangCode.SimplifiedChinese;
            if (lang.Equals("zh", System.StringComparison.InvariantCultureIgnoreCase))
                return LangCode.SimplifiedChinese;
            return lang;
        }

        public string GetPreferProvider()
        {
            //Todo: Calculate based on Country
            return ProviderCodeConstants.CloudFlare;
        }

        /// <summary>
        /// Gets the SpokenLangCode from the request header
        /// </summary>
        /// <returns>The spoken language code or null if not present</returns>
        public string? GetSpokenLangCode()
        {
            var lang = GetRequestHeader(SpokenLangCodeHeader) ?? SpokenLangCode.Mandarin;
            return lang;
        }

        public string? GetUserCountry()
        {
            //Get the country code from the request header
            return GetRequestHeader(UserCountryHeader);
        }

        public Guid? GetUserGuid()
        {
            var guidString = GetRequestHeader(UserGuidHeader);
            if (string.IsNullOrEmpty(guidString))
            {
                return null;
            }
            if (Guid.TryParse(guidString, out var parsedUserId))
            {
                return parsedUserId;
            }
            return null;
        }

        /// <summary>
        /// Gets both language codes as a tuple
        /// </summary>
        /// <returns>Tuple containing (LanguageCode, SpokenLangCode)</returns>
        public (string? LanguageCode, string? SpokenLangCode) GetLanguageCodes()
        {
            return (GetLanguageCode(), GetSpokenLangCode());
        }

        private string? GetRequestHeader(string headerName)
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.Request?.Headers == null)
            {
                return null;
            }
            //Try both the exact header name and the lowercase version
            if (httpContext.Request.Headers.TryGetValue(headerName, out var headerValue))
                return headerValue.ToString();
            if (httpContext.Request.Headers.TryGetValue(headerName.ToLower(), out var headerValue2))
                return headerValue2.ToString();
            return null;
        }
    }
}