﻿using Bogus;
using HolyBless.Entities.Collections;
using HolyBless.Enums;

namespace HolyBless.FakeData.DataSeeders
{
    public static class CollectionDataEngGenerator
    {
        // Extract leaf channel IDs from mainsiteEng.json structure
        private static readonly int[] LeafChannelIds = { 2011, 2021, 2022, 2013, 2041, 2042, 2051, 2052, 2061, 2062, 2063, 2064, 2065, 2066 };

        // Channel names mapping for context-appropriate collection names
        private static readonly Dictionary<int, string> ChannelNames = new()
    {
        { 2011, "Dream Series" },
        { 2021, "Heaven's Gate" },
        { 2022, "Blessing of Peace" },
        { 2013, "Warmth of Love" },
        { 2041, "Spiritual Overview" },
        { 2042, "Practice Q&A" },
        { 2051, "Blog" },
        { 2052, "Weibo" },
        { 2061, "Turn Back to the Shore" },
        { 2062, "Remembering the Teacher" },
        { 2063, "Practice Insights" },
        { 2064, "Review and Learn" },
        { 2065, "Sincere Repentance" },
        { 2066, "Light of Miracles" }
    };

        public static List<Collection> GenerateCollections()
        {
            var curId = 1;
            var faker = new Faker<Collection>("en")
                .RuleFor(c => c.Id, f => 2000 + curId++)
                .RuleFor(c => c.LanguageCode, f => LangCode.English)
                .RuleFor(c => c.ContentCode, f => $"C{curId - 1}")
                .RuleFor(c => c.ParentCollectionId, f => (int?)null) // Root collections
                .RuleFor(c => c.ChannelId, (f, c) => LeafChannelIds[f.IndexFaker % LeafChannelIds.Length])
                .RuleFor(c => c.Name, (f, c) => GenerateCollectionName(f, c.ChannelId!.Value))
                .RuleFor(c => c.Description, (f, c) => GenerateDescription(f, c.ChannelId!.Value))
                .RuleFor(c => c.Memo, f => f.Lorem.Sentence(3, 5))
                .RuleFor(c => c.Keywords, f => string.Join(",", f.Lorem.Words(3)))
                .RuleFor(c => c.Views, f => f.Random.Int(100, 50000))
                .RuleFor(c => c.Likes, f => f.Random.Int(10, 5000))
                .RuleFor(c => c.Weight, f => f.Random.Int(0, 100))
                .RuleFor(c => c.ListStyle, f => f.PickRandom<ListStyle>())
                //.RuleFor(c => c.RenderAsOneSet, f => f.Random.Bool(0.3f)) // 30% chance to render as one set
                .RuleFor(c => c.Status, f => f.PickRandom<PublishStatus>())
                .RuleFor(c => c.DefaultOrderBy, f => f.PickRandom<DefaultOrderByField>())
                .RuleFor(c => c.CreationTime, f => f.Date.Between(DateTime.Now.AddYears(-2), DateTime.Now))
                .RuleFor(c => c.LastModificationTime, (f, c) => f.Date.Between(c.CreationTime, DateTime.Now));

            return faker.Generate(LeafChannelIds.Length);
        }

        private static string GenerateCollectionName(Faker faker, int channelId)
        {
            if (!ChannelNames.TryGetValue(channelId, out _))
            {
                throw new ArgumentException($"Invalid channelId: {channelId}", nameof(channelId));
            }

            return channelId switch
            {
                2011 => faker.PickRandom(
                    "Dream Revelations", "Wisdom from Dreams", "Dreams and Reality", "Dreams Come True", "Dream Analysis",
                    "Power of Dreams", "Meditation in Dreams", "Dream Adventures", "Path of Dreams", "Dream Inspiration"
                ),
                2021 or 2022 => faker.PickRandom(
                    "Opening Heaven's Gate", "Sacred Gateway", "Path of Divine Revelation", "Blessings of Peace", "Divine Grace Abounds",
                    "Miracles of Heaven's Gate", "Sacred Protection", "Heavenly Blessings", "Divine Manifestations", "Light of Heaven's Gate"
                ),
                2013 => faker.PickRandom(
                    "Power of Love", "Warming Hearts", "Love and Compassion", "Miracles of Love", "Selfless Love",
                    "Temperature of Love", "Spreading Love", "Light of Love", "Love and Acceptance", "Essence of Love"
                ),
                2041 => faker.PickRandom(
                    "Spiritual Wisdom", "Soul Growth", "Spiritual Awakening", "Healing the Soul", "Purification of Spirit",
                    "Soul Insights", "Journey of the Soul", "Spiritual Enlightenment", "Peace of Mind", "Spiritual Transformation"
                ),
                2042 => faker.PickRandom(
                    "Practice Guidelines", "Spiritual Q&A", "Practice Doubts", "Spiritual Experiences", "Methods of Practice",
                    "Practice Journey", "Wisdom of Practice", "Spiritual Guidance", "Practice Challenges", "Spiritual Insights"
                ),
                2051 => faker.PickRandom(
                    "Life and Death Reflections", "Contemplations on Life", "Essence of Life", "Philosophy of Life and Death", "Life Insights",
                    "Meaning of Life", "Cycle of Life and Death", "Wisdom of Life", "Awakening to Life", "Transcending Life and Death"
                ),
                2052 => faker.PickRandom(
                    "Profound Microblog", "Life Reflections", "Daily Contemplations", "Microblog Essays", "Wisdom of Living",
                    "Random Thoughts", "Life Snippets", "Micro Observations", "Life Expressions", "Microblog Insights"
                ),
                2061 => faker.PickRandom(
                    "Turn Back to Shore", "Repentance and Renewal", "Fresh Start", "Finding the Way Back", "Prodigal's Return",
                    "Reformation and Goodness", "Rebirth and Renewal", "Return to the Right Path", "Repentant Rebirth", "New Beginning"
                ),
                2062 => faker.PickRandom(
                    "Teacher's Teachings", "Gratitude to Teacher", "Words of the Teacher", "Teacher's Compassion", "Teacher's Wisdom",
                    "Unforgettable Teacher", "Teacher's Love", "Master's Guidance", "Teacher's Virtue", "Deep Teacher's Grace"
                ),
                2063 => faker.PickRandom(
                    "Practice Reflections", "Spiritual Insights", "Practice Experiences", "Spiritual Journey", "Practice Gains",
                    "Spiritual Feelings", "Practice Encounters", "Spiritual Thoughts", "Practice Mindset", "Spiritual Realizations"
                ),
                2064 => faker.PickRandom(
                    "Review and Learn", "Looking Back", "Historical Review", "Past Memories", "Revisiting the Past",
                    "Reflecting on History", "Historical Insights", "Past and Present", "Memory Lane", "Historical Lessons"
                ),
                2065 => faker.PickRandom(
                    "Sincere Repentance", "Confession Records", "Book of Repentance", "Voice of Repentance", "Repentance Insights",
                    "Heartfelt Repentance", "Path of Repentance", "Repentant Rebirth", "Repentance Journal", "Words of Repentance"
                ),
                2066 => faker.PickRandom(
                    "Divine Manifestations", "Light of Miracles", "Witnessing Miracles", "Miracle Records", "Stories of Miracles",
                    "Experiencing Miracles", "Miracle Testimonies", "Miracle Insights", "Miraculous Life", "Divine Grace in Miracles"
                ),
                _ => string.Join(" ", faker.Lorem.Words(4))
            };
        }

        private static string GenerateDescription(Faker faker, int channelId)
        {
            var templates = channelId switch
            {
                2011 =>
                [
                "Recording revelations and wisdom from dreams, exploring the mysteries between dreams and reality.",
                "Sharing adventures and insights from dreams, analyzing the deeper meanings of our dream experiences.",
                "The world of dreams is full of mystery, documenting every detail of our dream journeys."
            ],
                2021 or 2022 =>
                [
                "Heaven's Gate opens, divine grace abounds, recording sacred moments and divine blessings.",
                "Witnessing the miracles of Heaven's Gate opening, experiencing sacred power and divine protection.",
                "Blessings of peace, divine grace bestowed, documenting the beautiful times when miracles manifest."
            ],
                2013 =>
                [
                "Love is the warmest force in the world, here we record every detail of love's manifestation.",
                "Sharing the warmth and power of love, spreading compassion and loving-kindness.",
                "Love can dissolve everything, this space is filled with the temperature and radiance of love."
            ],
                2041 =>
                [
                "Spiritual growth requires wisdom's guidance, here we record insights of the soul.",
                "Exploring the mysteries of the spirit, sharing experiences and wisdom of spiritual growth.",
                "The purification and transformation of the soul, documenting every detail of spiritual awakening."
            ],
                2042 =>
                [
                "Questions and answers on the spiritual path, providing direction for practitioners.",
                "Sharing experiences and insights from spiritual practice, answering doubts on the spiritual journey.",
                "The spiritual path is not easy, here we record the Q&A and guidance along the way."
            ],
                2051 =>
                [
                "Life and death like a river, human life like a boat, recording insights and contemplations about life.",
                "Exploring the mysteries of life and death, sharing wisdom and reflections on human existence.",
                "The meaning of life lies in experience, here we document every detail of life's journey."
            ],
                2052 =>
                [
                "Microblog records life's details, sharing daily insights and contemplations.",
                "Small insights from life, great wisdom from microblogs.",
                "Recording beautiful moments in life, sharing wisdom and insights from daily living."
            ],
                2061 =>
                [
                "Those who are lost can find their way back, turn back to shore, recording stories of fresh starts.",
                "Sharing experiences of repentance and renewal, witnessing miracles of rebirth.",
                "A prodigal's return is worth more than gold, here we record stories of reformation and goodness."
            ],
                2062 =>
                [
                "The teacher's teachings are like sweet dew, nourishing the hearts of disciples.",
                "Recording the teacher's compassion and wisdom, remembering the teacher's grace like mountains.",
                "The teacher's words are like bright lamps, illuminating the path forward."
            ],
                2063 =>
                [
                "Reflections and insights from the spiritual path, recording experiences from practice.",
                "Sharing gains and experiences from spiritual practice, documenting every detail of the journey.",
                "Spiritual practice is like climbing a mountain, here we record the mindset and feelings along the way."
            ],
                2064 =>
                [
                "Review the past to learn anew, looking back at past experiences and insights.",
                "Historical review and reflection, drawing wisdom from the past.",
                "Looking back at the past, understanding life, recording historical insights and lessons."
            ],
                2065 =>
                [
                "Sincere repentance, cleansing the soul, recording the voice of repentance.",
                "Heartfelt repentance, gaining new life, sharing insights and experiences of repentance.",
                "Repentance is like sweet dew, purifying the heart, documenting every detail of repentance."
            ],
                2066 =>
                [
                "Divine manifestations, light of miracles, recording testimonies of miracles.",
                "Witnessing the wonder of miracles, sharing stories and insights of miraculous experiences.",
                "Miracles are like bright lamps, illuminating the path forward, recording the grace of divine manifestations."
            ],
                _ => new[] { "Recording life's details, sharing insights and experiences from human existence." }
            };

            return faker.PickRandom(templates);
        }
    }
}