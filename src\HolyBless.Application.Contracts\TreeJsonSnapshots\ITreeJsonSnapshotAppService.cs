using System.Threading.Tasks;
using HolyBless.Enums;
using Volo.Abp.Application.Services;

namespace HolyBless.TreeJsonSnapshots
{
    public interface ITreeJsonSnapshotAppService : IApplicationService
    {
        Task<string> GetTreeJsonAsync(TreeType treeType, int? rootId = null);

        Task RefreshTreeJsonAsync(TreeType treeType, int? rootId = null);

        Task UpdateTreeJsonAsync(TreeType treeType, int? rootId, string treeJsonData);

        Task ClearCacheAsync(TreeType? treeType = null, int? rootId = null);
    }
}
