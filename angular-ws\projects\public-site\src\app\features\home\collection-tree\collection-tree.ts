import { Component, ElementRef, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TreeNode } from 'primeng/api';
import { TreeModule } from 'primeng/tree';
import { AccordionModule } from 'primeng/accordion';
import { ButtonModule } from 'primeng/button';
import { ActivatedRoute, Router } from '@angular/router';
import { ReadOnlyCollectionService } from '@/proxy/holy-bless/collections';
import { ReadOnlyArticleService } from '@/proxy/holy-bless/articles';
import { ArticleAggregateResult } from '@/proxy/holy-bless/results';
import { I18nService } from '@/services/i18n.service';
import { skip, Subscription } from 'rxjs';
import { LoadingService } from '@/services/loading.service';

@Component({
  selector: 'app-collection-tree',
  standalone: true,
  imports: [CommonModule, TreeModule, AccordionModule, ButtonModule],
  templateUrl: './collection-tree.html',
  styleUrls: ['./collection-tree.scss'],
})
export class CollectionTreeComponent {
  #route = inject(ActivatedRoute);
  #ReadOnlyCollectionService = inject(ReadOnlyCollectionService);
  #ReadOnlyArticleService = inject(ReadOnlyArticleService);
  elementRef = inject(ElementRef);
  i18nService = inject(I18nService);
  subs = new Subscription();
  loadingService = inject(LoadingService);
  router = inject(Router);

  collectionId: number | null = null;
  files: TreeNode[] = [];
  selectedFile!: TreeNode;
  items: ArticleAggregateResult[] = [];
  ngOnInit() {
    this.#route.params.subscribe((params) => {
      if (!params['collectionId']) return;
      this.collectionId = +params['collectionId'];
      this.changeLanguage(this.collectionId);
      this.loadCollectionSummary();
    });
  }

  changeLanguage(collectionId: number) {
    this.subs.unsubscribe();
    this.subs = new Subscription();
    const sub = this.i18nService.language$.pipe(skip(1)).subscribe(() => {
      this.#ReadOnlyCollectionService
        .getLanguageMatchingCollection(collectionId)
        .subscribe({
          next: (collection) => {
            if (!collection) {
              this.router.navigateByUrl(`/landing`);
              return;
            }
            if (collection.id !== collectionId) {
              this.router.navigateByUrl(`/home/<USER>/${collection.id}`);
            }
          },
        });
    });
    this.subs.add(sub);
  }

  loadCollectionSummary() {
    if (!this.collectionId) return;
    this.loadingService.show();
    this.#ReadOnlyCollectionService
      .getCollectionTree(this.collectionId)
      .subscribe({
        next: (data) => {
          this.files = data.map((node: any) => this.buildTreeNode(node));
          this.loadingService.hide();
        },
        error: (error) => {
          console.error('获取文章树数据失败:', error);
          this.loadingService.hide();
        },
      });
  }

  buildTreeNode(node: any): TreeNode {
    return {
      key: node.contentCode,
      label: node.name || node.title,
      data: node,
      children: node.children
        ? node.children.map((article: any) => this.buildTreeNode(article))
        : [],
    };
  }

  loadArticleSummary(node: TreeNode) {
    if (!node.key) return;
    this.#ReadOnlyArticleService
      .getArticleAggregatesByCollectionContentCode(node.key)
      .subscribe({
        next: (data) => {
          this.items = data;
        },
        error: (error) => {
          console.error('获取文章详情失败:', error);
        },
      });
  }

  onTocItemClick(item: ArticleAggregateResult) {
    this.scrollToAnchor('_Toc' + item.id);
  }

  private scrollToAnchor(anchorId: string) {
    const container = this.elementRef.nativeElement.querySelector(
      '.articaldetail-container',
    );
    if (!container) return;

    let targetElement = container.querySelector(
      `a[name="${anchorId}"]`,
    ) as HTMLElement;

    if (targetElement) {
      targetElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest',
      });
    } else {
      console.warn(`未找到锚点: ${anchorId}`);
    }
  }

  onPlayClick(item: ArticleAggregateResult) {
    console.log('Play clicked for item:', item);
    // 这里可以添加播放逻辑
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
