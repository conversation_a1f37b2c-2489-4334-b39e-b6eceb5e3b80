﻿using System;
using System.ComponentModel.DataAnnotations;
using HolyBless.Enums;

namespace HolyBless.Collections.Dtos
{
    public class CreateUpdateCollectionDto
    {
        public int? ParentCollectionId { get; set; }
        public string ContentCode { get; set; } = string.Empty;
        public string? LanguageCode { get; set; }

        [Required]
        public string Name { get; set; } = "";

        public string? Description { get; set; }
        public string? Keywords { get; set; }
        public int Views { get; set; }
        public int Likes { get; set; }
        public ListStyle? ListStyle { get; set; }

        public PublishStatus Status { get; set; }

        public string? Memo { get; set; }
        public DefaultOrderByField DefaultOrderBy { get; set; } = DefaultOrderByField.CreationTime;
    }
}