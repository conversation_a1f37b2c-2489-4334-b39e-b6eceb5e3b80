﻿using Volo.Abp.Application.Dtos;
using HolyBless.Enums;
using System;
using HolyBless.Buckets.Dtos;
using HolyBless.Interfaces;

namespace HolyBless.Collections.Dtos
{
    public class CollectionDto : EntityDto<int>
    {
        public int? ParentCollectionId { get; set; }
        public string ContentCode { get; set; } = ""; //Code value in ContentCode Table
        public string? LanguageCode { get; set; }
        public BucketFileDto? ThumbnailBucketFile { get; set; }
        public string Name { get; set; } = "";
        public string? Description { get; set; }
        public string? Keywords { get; set; }
        public int Views { get; set; }
        public int Likes { get; set; }
        public ListStyle? ListStyle { get; set; }

        //public CollectionType CollectionType { get; set; }

        //public DateTime? PublishDate { get; set; }
        public PublishStatus Status { get; set; }

        public string? Memo { get; set; }
        public DefaultOrderByField DefaultOrderBy { get; set; } = DefaultOrderByField.CreationTime;
    }
}