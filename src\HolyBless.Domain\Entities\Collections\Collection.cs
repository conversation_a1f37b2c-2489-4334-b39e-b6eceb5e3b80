﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Entities.Buckets;
using HolyBless.Entities.Channels;
using HolyBless.Entities.VirtualFolders;
using HolyBless.Enums;
using HolyBless.Lookups;
using Volo.Abp.Domain.Entities.Auditing;

namespace HolyBless.Entities.Collections
{
    public class Collection : DomainAuditedAggregateRoot<int>
    {
        public int? ParentCollectionId { get; set; }
        public Collection? ParentCollection { get; set; }

        //Only Root Collection could have ChannelId
        // If Collection is internal usage, root collection's channel Id could be null to

        public int? ChannelId { get; set; }
        public Channel? Channel { get; set; }
        public string Name { get; set; } = default!;
        public string? Description { get; set; }
        public string? Memo { get; set; } //For some notes, mostly internal use

        public string? Keywords { get; set; }
        public int Views { get; set; } = 0;
        public int Likes { get; set; } = 0;
        public int Weight { get; set; } = 0;
        public ListStyle? ListStyle { get; set; } = Enums.ListStyle.ImageCard; //Decide child collection display style

        public PublishStatus Status { get; set; } = PublishStatus.Draft;

        public DefaultOrderByField DefaultOrderBy { get; set; } = DefaultOrderByField.CreationTime;

        public ICollection<CollectionToArticle> CollectionToArticles { get; set; } = [];
        public ICollection<CollectionToFile> CollectionToFiles { get; set; } = [];
    }
}