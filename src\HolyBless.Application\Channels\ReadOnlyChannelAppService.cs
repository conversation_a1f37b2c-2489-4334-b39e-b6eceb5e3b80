using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Buckets;
using HolyBless.Channels.Dtos;
using HolyBless.Entities.Channels;
using HolyBless.Enums;
using HolyBless.Services;
using HolyBless.TreeJsonSnapshots;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Channels
{
    public class ReadOnlyChannelAppService : HolyBlessAppService, IReadOnlyChannelAppService
    {
        protected readonly IRepository<Channel, int> _repository;
        protected readonly ITreeJsonSnapshotAppService _treeSnapshotAppService;

        public ReadOnlyChannelAppService(
            IRepository<Channel, int> repository,
            ITreeJsonSnapshotAppService treeSnapshotAppService,
            IRequestContextService requestContextService,
            ICachedFileUrlAppService cachedFileUrlAppService
            ) : base(cachedFileUrlAppService, requestContextService)
        {
            _repository = repository;
            _treeSnapshotAppService = treeSnapshotAppService;
        }

        /// <summary>
        /// Usage: when language switch, get matched channel based on current channel Id's content code and language code (from request heander)
        /// </summary>
        /// <param name="channelId"></param>
        /// <returns></returns>
        public async Task<ChannelDto?> GetMatchedChannelAsync(int channelId)
        {
            var channel = await _repository.FirstOrDefaultAsync(x => x.Id == channelId);
            if (channel == null) return null;
            return await GetMatchedChannelByContentCodeAsync(channel.ContentCode ?? "");
        }

        /// <summary>
        /// Get a channel by its content code and language code (from request header).
        /// </summary>
        /// <param name="contentCode"></param>
        /// <returns></returns>
        public async Task<ChannelDto?> GetMatchedChannelByContentCodeAsync(string contentCode)
        {
            var lang = _requestContextService!.GetLanguageCode();
            var channel = await _repository.FirstOrDefaultAsync(x => x.ContentCode == contentCode && x.LanguageCode == lang);
            if (channel == null) return null;
            return ObjectMapper.Map<Channel, ChannelDto>(channel);
        }

        /// <summary>
        /// Get a channel by book ID. Uses Channel's EBooks navigation property to find the channel associated with the book.
        /// </summary>
        /// <param name="bookId"></param>
        /// <returns></returns>
        public async Task<ChannelDto?> GetMatchedChannelByBookIdAsync(int bookId)
        {
            // Find the channel that has a book with the specified ID using navigation property
            var queryable = await _repository.GetQueryableAsync();
            var channel = await queryable
                .FirstOrDefaultAsync(c => c.EBooks.Any(book => book.Id == bookId));

            if (channel?.ContentCode == null) return null;

            // Use the existing method to get the matched channel by content code
            return await GetMatchedChannelByContentCodeAsync(channel.ContentCode);
        }

        /// <summary>
        /// Get a channel by album ID. Uses Channel's Albums navigation property to find the channel associated with the album.
        /// </summary>
        /// <param name="albumId"></param>
        /// <returns></returns>
        public async Task<ChannelDto?> GetMatchedChannelByAlbumIdAsync(int albumId)
        {
            // Find the channel that has an album with the specified ID using navigation property
            var queryable = await _repository.GetQueryableAsync();
            var channel = await queryable
                .FirstOrDefaultAsync(c => c.Albums.Any(album => album.Id == albumId));

            if (channel?.ContentCode == null) return null;

            // Use the existing method to get the matched channel by content code
            return await GetMatchedChannelByContentCodeAsync(channel.ContentCode);
        }

        /// <summary>
        /// Get a channel by virtual folder ID. Uses Channel's VirtualDiskFolders navigation property to find the channel associated with the virtual folder.
        /// </summary>
        /// <param name="virtualFolderId"></param>
        /// <returns></returns>
        public async Task<ChannelDto?> GetMatchedChannelByVirtualFolderIdAsync(int virtualFolderId)
        {
            // Find the channel that has a virtual folder with the specified ID using navigation property
            var queryable = await _repository.GetQueryableAsync();
            var channel = await queryable
                .FirstOrDefaultAsync(c => c.VirtualDiskFolders.Any(folder => folder.Id == virtualFolderId));

            if (channel?.ContentCode == null) return null;

            // Use the existing method to get the matched channel by content code
            return await GetMatchedChannelByContentCodeAsync(channel.ContentCode);
        }

        /// <summary>
        /// Get the channel tree structure for a specific language.
        /// Usage: Render UI top main menu and its menu items.
        /// </summary>
        /// <param name="languageCode"></param>
        /// <returns></returns>
        public virtual async Task<List<ChannelTreeDto>> GetChannelTreeAsync(string languageCode)
        {
            // Try to read cached snapshot first
            try
            {
                var json = await _treeSnapshotAppService.GetTreeJsonAsync(TreeType.Channel, null);
                if (!string.IsNullOrWhiteSpace(json))
                {
                    var opts = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                    var cached = System.Text.Json.JsonSerializer.Deserialize<List<ChannelTreeDto>>(json, opts);
                    if (cached != null && cached.Count > 0) return cached;
                }
            }
            catch
            {
                // ignore and build from DB
            }

            var queryable = await _repository.GetQueryableAsync();
            queryable = queryable
                .Where(x => x.LanguageCode.ToLower() == languageCode.ToLower())
                .OrderBy(x => x.ParentChannelId).ThenBy(x => x.Weight);

            var allChannels = await AsyncExecuter.ToListAsync(queryable);

            // Convert all channels to ChannelTreeDto
            var channelDtos = ObjectMapper.Map<List<Channel>, List<ChannelTreeDto>>(allChannels);

            // Create a dictionary for quick lookup
            var channelDict = channelDtos.ToDictionary(x => x.Id);

            // Build the tree structure
            var rootChannels = new List<ChannelTreeDto>();

            foreach (var channel in channelDtos)
            {
                var parentChannel = allChannels.FirstOrDefault(x => x.Id == channel.Id)?.ParentChannelId;

                if (parentChannel.HasValue && channelDict.TryGetValue(parentChannel.Value, out var parentDto))
                {
                    if (channel.ChannelSource == null)
                    {
                        channel.ChannelSource = parentDto.ChannelSource;
                    }
                    // Add as child to parent
                    parentDto.Children.Add(channel);
                }
                else if (channel.IsRoot)
                {
                    // Add as root channel
                    rootChannels.Add(channel);
                }
            }

            return rootChannels;
        }
    }
}