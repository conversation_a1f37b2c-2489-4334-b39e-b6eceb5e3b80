import { inject, Injectable } from '@angular/core';
import { VideoCacheService } from './video-cache.service';
import <PERSON><PERSON><PERSON><PERSON> from 'jszip';
import { saveAs } from 'file-saver';

@Injectable({
  providedIn: 'root',
})
export class DownloadService {
  #VideoCacheService = inject(VideoCacheService);

  async downloadFile(url: string, fileName: string) {
    if (!url) return;
    const cached = await this.#VideoCacheService.getCachedVideo(url);
    let downloadUrl = url;
    if (cached) {
      downloadUrl = cached.blob ? URL.createObjectURL(cached.blob) : url;
    }
    try {
      const a = document.createElement('a');
      a.href = downloadUrl;
      a.download = fileName || this.extractFileName(downloadUrl) || 'download';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      if (!cached) {
        await this.#VideoCacheService.smartCacheVideo(downloadUrl, a.download);
      }
    } catch (e) {
      console.error('Download failed:', e);
      // 如果创建下载链接失败，直接打开URL
      window.open(downloadUrl, '_blank');
    }
  }

  private extractFileName(downloadUrl: string): string {
    try {
      const url = new URL(downloadUrl);
      const pathname = url.pathname;
      const fileName = pathname.split('/').pop();
      return fileName || 'download';
    } catch {
      return downloadUrl.split('/').pop() || 'download';
    }
  }

  async batchDownloadAsZip(
    files: { fileUrl: string; fileName: string }[],
    zipName: string = 'batch-download.zip',
  ) {
    if (files.length === 0) return;
    const zip = new JSZip();

    try {
      // 并发下载所有文件
      const downloadPromises = files.map(async ({ fileUrl, fileName }) => {
        try {
          await this.#VideoCacheService.smartCacheVideo(fileUrl, fileName);
          const cached = await this.#VideoCacheService.getCachedVideo(fileUrl);
          if (cached) {
            return { fileName, blob: cached.blob };
          }
          return null;
        } catch (error) {
          console.error(`Failed to download ${fileName}:`, error);
          return null;
        }
      });

      const results = await Promise.allSettled(downloadPromises);

      // 将成功下载的文件添加到 ZIP
      results.forEach((result) => {
        if (result.status === 'fulfilled' && result.value) {
          const { fileName, blob } = result.value;
          zip.file(fileName, blob);
        }
      });

      // 生成并下载 ZIP 文件
      const zipBlob = await zip.generateAsync({ type: 'blob' });
      saveAs(zipBlob, zipName);
    } catch (error) {
      console.error('Batch download failed:', error);
    }
  }
}
