import { Environment } from '@abp/ng.core';

const baseUrl = 'https://holybless-public.pages.dev/';// 'http://localhost:4200';

const oAuthConfig = {
  issuer: 'https://dev.holyblesspan.com/',//'https://localhost:44362/',
  redirectUri: baseUrl,
  clientId: 'holybless_App',
  responseType: 'code',
  scope: 'offline_access holybless',
  requireHttps: true,
};

export const environment = {
  production: true,
  application: {
    baseUrl,
    name: 'holybless',
  },
  oAuthConfig,
  apis: {
    default: {
      url: 'https://dev.holyblesspan.com',//'https://localhost:44362',  //can not have ending /
      rootNamespace: 'Holybless',
    },
    AbpAccountPublic: {
      url: oAuthConfig.issuer,
      rootNamespace: 'AbpAccountPublic',
    },
  }
  //remoteEnv: {
  //  url: '/getEnvConfig',
  //  mergeStrategy: 'deepmerge'
  //}
} as Environment;
