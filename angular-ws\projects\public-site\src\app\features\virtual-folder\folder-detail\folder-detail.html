<div class="flex flex-1">
  <p-tree
    [value]="files()"
    styleClass="w-[20rem] h-full"
    selectionMode="single"
    [(selection)]="selectedNode"
    [virtualScroll]="true"
    virtualScrollItemSize="36"
    [filter]="true"
    (onNodeSelect)="loadFiles($event.node)"
  />
  <div
    class="articaldetail-container prose max-w-none p-6 flex-1 overflow-y-auto"
    style="height: calc(100vh - 5rem)"
  >
    <p-table [value]="products()" [(selection)]="selectedProducts">
      <ng-template #caption>
        <div class="flex items-center gap-2">
          <p-button
            icon="pi pi-cloud-download"
            label="下载"
            [outlined]="true"
            (onClick)="downloadService.batchDownloadAsZip(selectedProducts)"
          />
          <p-button icon="pi pi-play-circle" label="播放" [outlined]="true" />
        </div>
      </ng-template>
      <ng-template #header>
        <tr>
          <th style="width: 4rem"><p-tableHeaderCheckbox /></th>
          <th>名称</th>
          <th>类型</th>
          <th>更新时间</th>
          <th></th>
        </tr>
      </ng-template>
      <ng-template #body let-product>
        <tr>
          <td>
            <p-tableCheckbox [value]="product" />
          </td>
          <td>{{ product.title || (product.fileName | removeExtension) }}</td>
          <td>{{ MediaType[product.mediaType] }}</td>
          <td>
            {{ product.lastModificationTime | date: 'yyyy-MM-dd HH:mm:ss' }}
          </td>
          <td>
            <div class="flex items-center gap-2">
              <p-button
                size="small"
                icon="pi pi-cloud-download"
                [outlined]="true"
                [appDownload]="product.fileUrl"
                [downloadName]="product.fileName"
              />
              <p-button
                size="small"
                icon="pi pi-play-circle"
                [outlined]="true"
                (onClick)="playVideo(product)"
              />
            </div>
          </td>
        </tr>
      </ng-template>
      <ng-template #footer> </ng-template>
    </p-table>
    <div class="flex justify-end mt-4">
      <p-paginator
        [rows]="10"
        [totalRecords]="totalRecords()"
        [first]="first()"
        (onPageChange)="onPageChange($event)"
      />
    </div>
  </div>
</div>
