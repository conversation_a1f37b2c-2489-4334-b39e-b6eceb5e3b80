@page
@using Volo.Abp.AspNetCore.Mvc.UI.Theming
@inject HolyBless.Configs.AppConfig AppConfig
@inject ITheme Theme
@model Volo.Abp.Account.Web.Pages.Account.LoginModel
@{
    Layout = Theme.GetEmptyLayout();
    var isDisabled = !AppConfig.ExposeWritableApi;
    var actionsClass = isDisabled ? "d-flex justify-content-end align-items-center" : "d-flex justify-content-between align-items-center";
}

<div class="d-flex align-items-center" style="min-height: 100vh;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-5">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <h3 class="mb-4 text-center">Sign in</h3>
                        <form method="post" @(isDisabled ? "onsubmit='return false;'" : "")>
                            @Html.AntiForgeryToken()
                            <input type="hidden" name="ReturnUrl" value="@Model.ReturnUrl" />
                            <input type="hidden" name="ReturnUrlHash" value="@Model.ReturnUrlHash" />

                            <div class="mb-3">
                                <label class="form-label">Email or username</label>
                                <input class="form-control" name="Input.UserNameOrEmailAddress" autocomplete="username" />
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Password</label>
                                <input type="password" class="form-control" name="Input.Password" autocomplete="current-password" />
                            </div>

                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="Input.RememberMe" id="rememberMe" />
                                <label class="form-check-label" for="rememberMe">Remember me</label>
                            </div>

                            <div class="@actionsClass">
                                @if (!isDisabled)
                                {
                                    <a href="/Account/ForgotPassword" class="text-decoration-none">Forgot password?</a>
                                }
                                <button type="submit" class="btn btn-primary" @(isDisabled ? "disabled" : "")>Sign in</button>
                            </div>

                            @* Register link intentionally removed *@
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

