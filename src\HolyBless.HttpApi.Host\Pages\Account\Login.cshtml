@page
@using Volo.Abp.AspNetCore.Mvc.UI.Theming
@inject ITheme Theme
@model HolyBless.Pages.Account.LoginModel
@{
    Layout = Theme.GetEmptyLayout();
}

<div class="d-flex align-items-center" style="min-height: 100vh;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-5">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <h3 class="mb-4 text-center">Sign in</h3>

                        @if (!ViewData.ModelState.IsValid)
                        {
                            <div asp-validation-summary="All" class="alert alert-danger"></div>
                        }

                        @if (Model.IsDisabled)
                        {
                            <form method="post" onsubmit="return false;">
                                @Html.AntiForgeryToken()
                                <input type="hidden" name="ReturnUrl" value="@Model.ReturnUrl" />
                                <input type="hidden" name="ReturnUrlHash" value="@Model.ReturnUrlHash" />

                                <div class="mb-3">
                                    <label class="form-label">Email or username</label>
                                    <input class="form-control" name="Input.UserNameOrEmailAddress" autocomplete="username" />
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Password</label>
                                    <input type="password" class="form-control" name="Input.Password" autocomplete="current-password" />
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="Input.RememberMe" id="rememberMe" />
                                    <label class="form-check-label" for="rememberMe">Remember me</label>
                                </div>

                                <div class="@Model.ActionsClass">
                                    <button type="submit" class="btn btn-primary" disabled>Sign in</button>
                                </div>

                                @* Register link intentionally removed *@
                            </form>
                        }
                        else
                        {
                            <form method="post" asp-page-handler="Login">
                                @Html.AntiForgeryToken()
                                <input type="hidden" asp-for="ReturnUrl" />
                                <input type="hidden" asp-for="ReturnUrlHash" />

                                <div class="mb-3">
                                    <label asp-for="LoginInput.UserNameOrEmailAddress" class="form-label">Email or username</label>
                                    <input asp-for="LoginInput.UserNameOrEmailAddress" class="form-control" autocomplete="username" />
                                    <span asp-validation-for="LoginInput.UserNameOrEmailAddress" class="text-danger"></span>
                                </div>

                                <div class="mb-3">
                                    <label asp-for="LoginInput.Password" class="form-label">Password</label>
                                    <input asp-for="LoginInput.Password" type="password" class="form-control" autocomplete="current-password" />
                                    <span asp-validation-for="LoginInput.Password" class="text-danger"></span>
                                </div>

                                <div class="form-check mb-3">
                                    <input asp-for="LoginInput.RememberMe" class="form-check-input" id="rememberMe" />
                                    <label asp-for="LoginInput.RememberMe" class="form-check-label">Remember me</label>
                                </div>

                                <div class="@Model.ActionsClass">
                                    <a href="/Account/ForgotPassword" class="text-decoration-none">Forgot password?</a>
                                    <button type="submit" class="btn btn-primary">Sign in</button>
                                </div>

                                @* Register link intentionally removed *@
                            </form>

                            @* External login providers (only when enabled) *@
                            @if (Model.ExternalProviders?.Any() ?? false)
                            {
                                <hr class="my-4" />
                                <div class="text-center text-muted mb-2">Or continue with</div>
                                <form method="post" asp-page-handler="ExternalLogin">
                                    <input type="hidden" asp-for="ReturnUrl" />
                                    <input type="hidden" asp-for="ReturnUrlHash" />
                                    <div class="d-flex flex-wrap gap-2 justify-content-center">
                                        @foreach (var provider in Model.ExternalProviders!)
                                        {
                                            <button type="submit" name="provider" value="@provider.AuthenticationScheme" class="btn btn-outline-secondary">
                                                @provider.DisplayName
                                            </button>
                                        }
                                    </div>
                                </form>
                            }
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section scripts {
    <partial name="_ValidationScriptsPartial" />
}