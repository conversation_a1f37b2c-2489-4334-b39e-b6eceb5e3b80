﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Enums;
using Volo.Abp.Domain.Entities.Auditing;

namespace HolyBless.Entities.VirtualFolders
{
    public class TreeJsonSnapshot : FullAuditedAggregateRoot<int>
    {
        public TreeType TreeType { get; set; }

        //For Channel RootId is null, For EBook, it is BookId, For Folder, it is root Folder Id
        public int? RootId { get; set; }
        public string TreeJsonData { get; set; } = string.Empty;
    }
}