import { Component, computed, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { ReadOnlyArticleService } from '@/proxy/holy-bless/articles';
import { ArticleAggregateResult } from '@/proxy/holy-bless/results';
import { GalleriaModule } from 'primeng/galleria';
import { MediaType } from '@/proxy/holy-bless/enums';
import { I18nService } from '@/services/i18n.service';
import { skip, Subscription } from 'rxjs';
import { LoadingService } from '@/services/loading.service';
import { PlayerService } from '@/services/player.service';
import { PlayDownloadComponent } from '@/components/play-download/play-download';

@Component({
  selector: 'app-artical-detail',
  standalone: true,
  imports: [CommonModule, GalleriaModule, PlayDownloadComponent],
  templateUrl: './article-detail.html',
  styleUrls: ['./article-detail.scss'],
})
export class ArticleDetailComponent {
  #route = inject(ActivatedRoute);
  #ReadOnlyArticleService = inject(ReadOnlyArticleService);
  articleId: string | null = null;
  i18nService = inject(I18nService);
  subs = new Subscription();
  loadingService = inject(LoadingService);
  router = inject(Router);
  playerService = inject(PlayerService);

  articleDetail = signal<ArticleAggregateResult | null>(null);
  articleFiles = computed(() => this.articleDetail()?.articleFiles || []);
  primaryArticleFiles = computed(() =>
    this.articleFiles().filter((file) => file.isPrimary === true),
  );
  imageArticleFiles = computed(() =>
    this.articleFiles().filter((file) => file.mediaType === MediaType.Image),
  );
  // imageArticleFiles = signal([
  //   { fileUrl: 'https://picsum.photos/200/300' },
  //   { fileUrl: 'https://picsum.photos/300/400' },
  //   { fileUrl: 'https://picsum.photos/400/200' },
  // ]);
  notImageArticleFiles = computed(() =>
    this.articleFiles().filter((file) => file.mediaType !== MediaType.Image),
  );

  ngOnInit() {
    this.#route.params.subscribe((params) => {
      if (!params['articleId']) return;
      this.articleId = params['articleId'];
      if (this.articleId) {
        this.loadArticleDetail();
        this.changeLanguage(+this.articleId);
      }
    });
  }

  loadArticleDetail() {
    if (!this.articleId) return;
    this.loadingService.show();
    this.#ReadOnlyArticleService
      .getArticleAggregate(+this.articleId)
      .subscribe({
        next: (data) => {
          this.articleDetail.set(data);
          this.loadingService.hide();
        },
        error: (error) => {
          console.error('获取文章详情失败:', error);
          this.loadingService.hide();
        },
      });
  }

  changeLanguage(articleId: number | null) {
    if (!articleId) return;
    this.subs.unsubscribe();
    this.subs = new Subscription();
    const sub = this.i18nService.language$.pipe(skip(1)).subscribe((lang) => {
      this.#ReadOnlyArticleService
        .getMatchedIdByArticleId(articleId)
        .subscribe({
          next: (id) => {
            if (!id) {
              this.router.navigateByUrl('/landing');
              return;
            }
            this.router.navigateByUrl(`/home/<USER>/${id}`);
          },
        });
    });
    this.subs.add(sub);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
