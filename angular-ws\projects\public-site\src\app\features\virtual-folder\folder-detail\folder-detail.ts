import { DownloadDirective } from '@/directives/download.directive';
import { RemoveExtensionPipe } from '@/pipes/remove-extension.pipe';
import { ReadOnlyChannelService } from '@/proxy/holy-bless/channels';
import { MediaType } from '@/proxy/holy-bless/enums';
import {
  ReadOnlyVirtualFolderService,
  VirtualFolderTreeDto,
} from '@/proxy/holy-bless/virtual-folders';
import { DownloadService } from '@/services/download.service';
import { I18nService } from '@/services/i18n.service';
import { LoadingService } from '@/services/loading.service';
import { PlayerService } from '@/services/player.service';
import { CommonModule } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { TreeNode } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { PaginatorModule } from 'primeng/paginator';
import { TableModule } from 'primeng/table';
import { TreeModule } from 'primeng/tree';
import { skip, Subscription } from 'rxjs';

@Component({
  selector: 'app-net-disk',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TreeModule,
    ButtonModule,
    TableModule,
    DownloadDirective,
    PaginatorModule,
    RemoveExtensionPipe,
  ],
  templateUrl: './folder-detail.html',
  styleUrls: ['./folder-detail.scss'],
})
export class FolderDetailComponent {
  MediaType = MediaType;
  first = signal(0);
  rows = signal(10);
  totalRecords = signal(0);
  #ReadOnlyVirtualFolderService = inject(ReadOnlyVirtualFolderService);
  #ReadOnlyChannelService = inject(ReadOnlyChannelService);
  route = inject(ActivatedRoute);
  router = inject(Router);
  i18nService = inject(I18nService);
  subs = new Subscription();
  loadingService = inject(LoadingService);
  playerService = inject(PlayerService);
  downloadService = inject(DownloadService);
  selectedProducts: any[] = [];

  files = signal<TreeNode[]>([]);

  selectedNode!: TreeNode;
  products = signal<any[]>([]);
  ngOnInit() {
    this.route.params.subscribe((params) => {
      const folderId = params['folderId'];
      this.loadFolderDetails(folderId);
      if (folderId) {
        this.changeLanguage(folderId);
      }
    });
  }

  changeLanguage(folderId: number) {
    this.subs.unsubscribe();
    this.subs = new Subscription();
    const sub = this.i18nService.language$.pipe(skip(1)).subscribe((lang) => {
      this.#ReadOnlyChannelService
        .getMatchedChannelByVirtualFolderId(folderId)
        .subscribe({
          next: (channel) => {
            if (!channel) {
              this.router.navigateByUrl('/landing');
              return;
            }
            this.router.navigateByUrl(`/virtual-folder/list/${channel.id}`);
          },
        });
    });
    this.subs.add(sub);
  }

  loadFolderDetails(folderId: number) {
    if (!folderId) return;
    this.loadingService.show();
    this.#ReadOnlyVirtualFolderService
      .getVirtualFolderTree(folderId)
      .subscribe({
        next: (data) => {
          this.files.set(data.map((folder) => this.buildTreeNode(folder)));
          this.loadingService.hide();
        },
        error: (error) => {
          console.error('获取文件夹详情失败:', error);
          this.loadingService.hide();
        },
      });
  }

  buildTreeNode(folder: VirtualFolderTreeDto): TreeNode {
    return {
      key: folder.id + '',
      label: folder.folderName,
      data: folder,
      children: folder.children.map((child) => this.buildTreeNode(child)),
    };
  }

  loadFiles(node: TreeNode) {
    if (!node.key) return;
    this.selectedNode = node;
    this.loadingService.show();
    this.#ReadOnlyVirtualFolderService
      .getFolderFiles({
        folderId: +node.key,
        skipCount: this.first(),
        maxResultCount: this.rows(),
      })
      .subscribe({
        next: (data) => {
          this.products.set(data.items || []);
          this.totalRecords.set(data.totalCount || 0);
          this.loadingService.hide();
        },
        error: (error) => {
          console.error('获取文章详情失败:', error);
          this.loadingService.hide();
        },
      });
  }

  onPageChange(event: any) {
    this.first.set(event.first);
    this.rows.set(event.rows);
    this.loadFiles(this.selectedNode);
  }

  playVideo(product: any) {
    this.playerService.playVideo(product);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
