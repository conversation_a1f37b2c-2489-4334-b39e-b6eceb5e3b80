using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Buckets;
using HolyBless.Channels.Dtos;
using HolyBless.Entities.Channels;
using HolyBless.Permissions;
using HolyBless.Services;
using HolyBless.TreeJsonSnapshots;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Channels
{
    [Authorize(HolyBlessPermissions.Channels.Default)]
    public class ChannelAppService : ReadOnlyChannelAppService, IChannelAppService
    {
        public ChannelAppService(IRepository<Channel, int> repository,
            ITreeJsonSnapshotAppService treeSnapshotAppService,
            IRequestContextService requestContextService,
            ICachedFileUrlAppService cachedFileUrlAppService
            )
            : base(repository, treeSnapshotAppService, requestContextService, cachedFileUrlAppService)
        {
        }

        [Authorize(HolyBlessPermissions.Channels.Create)]
        public async Task<ChannelDto> CreateAsync(CreateUpdateChannelDto input)
        {
            var channel = ObjectMapper.Map<CreateUpdateChannelDto, Channel>(input);
            channel = await _repository.InsertAsync(channel, autoSave: true);
            return ObjectMapper.Map<Channel, ChannelDto>(channel);
        }

        [Authorize(HolyBlessPermissions.Channels.Edit)]
        public async Task<ChannelDto> UpdateAsync(int id, CreateUpdateChannelDto input)
        {
            var channel = await _repository.GetAsync(id);
            ObjectMapper.Map(input, channel);
            channel = await _repository.UpdateAsync(channel, true);
            return ObjectMapper.Map<Channel, ChannelDto>(channel);
        }

        [Authorize(HolyBlessPermissions.Channels.Delete)]
        public async Task DeleteAsync(int id)
        {
            var channel = await _repository.GetAsync(id, includeDetails: true);
            if (channel.Collection != null)
            {
                throw new UserFriendlyException(L["CannotDeleteChannelWithCollections"]);
            }
            if (channel.EBooks != null && channel.EBooks.Any())
            {
                throw new UserFriendlyException(L["CannotDeleteChannelWithEBooks"]);
            }
            if (channel.Albums != null && channel.Albums.Any())
            {
                throw new UserFriendlyException(L["CannotDeleteChannelWithAlbums"]);
            }
            if (channel.VirtualDiskFolders != null && channel.VirtualDiskFolders.Any())
            {
                throw new UserFriendlyException(L["CannotDeleteChannelWithVirtualFolders"]);
            }
            if (channel.ChildChannels != null && channel.ChildChannels.Any())
            {
                throw new UserFriendlyException(L["CannotDeleteChannelWithChildChannels"]);
            }
            await _repository.DeleteAsync(id, true);
        }

        /// <summary>
        /// [Admin] Get a channel by its ID. This method is used to retrieve a specific channel's details.
        /// Usage: Pop up a dialog to show channel details, or when editing a channel.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ChannelDto> GetAsync(int id)
        {
            var channel = await _repository.FirstOrDefaultAsync(x => x.Id == id);
            Check.NotNull(channel, nameof(channel));
            var channelDto = ObjectMapper.Map<Channel, ChannelDto>(channel);
            return channelDto;
        }

        /// <summary>
        /// [Admin] Get a paginated list of channels based on search criteria, additional filter by lannguage code (from request header).
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<ChannelDto>> GetListAsync(ChannelSearchDto input)
        {
            var languageCode = _requestContextService!.GetLanguageCode();
            var queryable = await _repository.GetQueryableAsync();
            var query = queryable
                .WhereIf(!string.IsNullOrWhiteSpace(input.ContentCode), x => (x.ContentCode ?? "").ToLower() == input.ContentCode!.ToLower())
                .WhereIf(!string.IsNullOrWhiteSpace(languageCode), x => (x.LanguageCode ?? "").ToLower() == languageCode!.ToLower())
                .OrderBy(input.Sorting ?? "Weight")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount);

            var channels = await AsyncExecuter.ToListAsync(query);
            var totalCount = await AsyncExecuter.CountAsync(queryable);

            return new PagedResultDto<ChannelDto>(
                totalCount,
                ObjectMapper.Map<List<Channel>, List<ChannelDto>>(channels)
            );
        }

        /// <summary>
        /// [Admin] Get all channels without pagination, filtered by language code (from request header).
        /// </summary>
        /// <returns></returns>
        public async Task<List<ChannelDto>> GetAllChannelsAsync()
        {
            var languageCode = _requestContextService!.GetLanguageCode();

            var queryable = await _repository.GetQueryableAsync();
            var query = queryable
                .Where(x => x.LanguageCode == null || x.LanguageCode.ToLower() == (languageCode ?? "").ToLower())
                .OrderBy(x => x.Weight);
            var channels = await AsyncExecuter.ToListAsync(query);
            return ObjectMapper.Map<List<Channel>, List<ChannelDto>>(channels);
        }
    }
}